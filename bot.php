<?php

// Include configuration file
require_once 'config.php';
require_once 'database.php';

define('API_KEY', $API_KEY);

function bot($method, $datas = [])
{
    $url = "https://api.telegram.org/bot" . API_KEY . "/" . $method;
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $datas);
    $res = curl_exec($ch);
    if (curl_error($ch)) {
        var_dump(curl_error($ch));
    } else {
        return json_decode($res);
    }
}

function sendmessage($chat_id, $text, $reply_markup = null)
{
    $data = [
        'chat_id' => $chat_id,
        'text' => $text
    ];

    if ($reply_markup) {
        $data['reply_markup'] = $reply_markup;
    }

    bot('sendMessage', $data);
}

function checkUserMembership($user_id, $channels)
{
    foreach ($channels as $channel) {
        $result = bot('getChatMember', [
            'chat_id' => $channel,
            'user_id' => $user_id
        ]);

        if (!$result || !isset($result->result) ||
            in_array($result->result->status, ['left', 'kicked'])) {
            return false;
        }
    }
    return true;
}

function createJoinKeyboard($channels)
{
    $keyboard = [];

    // Create a single row with all channel buttons side by side
    $channel_buttons = [];
    $channel_names = ['کانال اول', 'کانال دوم'];

    foreach ($channels as $index => $channel) {
        $channel_name = str_replace('@', '', $channel);
        $button_text = isset($channel_names[$index]) ? $channel_names[$index] : "کانال " . ($index + 1);
        $channel_buttons[] = [
            'text' => "🔔 عضویت در $button_text",
            'url' => "https://t.me/$channel_name"
        ];
    }
    // Reverse the array to put "کانال اول" on the right
    $channel_buttons = array_reverse($channel_buttons);
    $keyboard[] = $channel_buttons;

    // Add check membership button in a separate row
    $keyboard[] = [
        [
            'text' => '🔄 بررسی عضویت',
            'callback_data' => 'check_membership'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function createMainMenuKeyboard()
{
    $keyboard = [
        [
            [
                'text' => '📣 چنل های من',
                'callback_data' => 'my_channels'
            ],
            [
                'text' => '➕ افزودن چنل',
                'callback_data' => 'add_channel'
            ]
        ],
        [
            [
                'text' => '📚 راهنما',
                'callback_data' => 'help'
            ]
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

function getMainMenuMessage($first_name)
{
    return "سلام $first_name 👋\n\n" .
           "به ربات پست ادیتور خوش آمدید!\n\n" .
           "این ربات به شما کمک می‌کند تا پست های کانال خود را به صورت کاملا خودکار ویرایش کنید.\n\n" .
           "<blockquote>📚 لطفا مراحل استفاده از ربات را از طریق دکمه راهنما و یا دستور /help مطالعه کنید.</blockquote>";
}

function checkChannelAdmin($user_id, $channel_id)
{
    $result = bot('getChatMember', [
        'chat_id' => $channel_id,
        'user_id' => $user_id
    ]);

    if ($result && isset($result->result)) {
        $status = $result->result->status;
        return in_array($status, ['creator', 'administrator']);
    }
    return false;
}

function getChannelInfo($channel_id)
{
    $result = bot('getChat', [
        'chat_id' => $channel_id
    ]);

    if ($result && isset($result->result)) {
        return [
            'id' => $result->result->id,
            'title' => $result->result->title ?? '',
            'username' => isset($result->result->username) ? '@' . $result->result->username : '',
            'type' => $result->result->type
        ];
    }
    return false;
}

function createMyChannelsKeyboard($user_id)
{
    global $database;
    $channels = $database->getUserChannels($user_id);

    $keyboard = [];

    if (empty($channels)) {
        $keyboard[] = [
            [
                'text' => '❌ هیچ کانالی ثبت نشده است',
                'callback_data' => 'no_channels'
            ]
        ];
    } else {
        foreach ($channels as $channel) {
            $title = $channel['channel_title'] ?: $channel['channel_username'] ?: $channel['channel_id'];
            $admin_status = $channel['is_admin'] ? '✅' : '❌';
            $keyboard[] = [
                [
                    'text' => "$admin_status $title",
                    'callback_data' => 'channel_' . $channel['id']
                ]
            ];
        }
    }

    // Add back button
    $keyboard[] = [
        [
            'text' => '🔙 بازگشت به منوی اصلی',
            'callback_data' => 'back_to_main'
        ]
    ];

    return json_encode(['inline_keyboard' => $keyboard]);
}

$update = json_decode(file_get_contents('php://input'));

// Handle callback queries (button presses)
if (isset($update->callback_query)) {
    $callback_query = $update->callback_query;
    $user_id = $callback_query->from->id;
    $chat_id = $callback_query->message->chat->id;
    $data = $callback_query->data;

    if ($data == 'check_membership') {
        $join_keyboard = createJoinKeyboard($required_channels);

        // First, show checking message with buttons
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "⌛️ در حال بررسی عضویت شما...",
            'reply_markup' => $join_keyboard
        ]);

        // Small delay to show the checking message
        sleep(1);

        if (checkUserMembership($user_id, $required_channels)) {
            $first_name = $callback_query->from->first_name ?? 'کاربر';
            $main_menu_keyboard = createMainMenuKeyboard();
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => getMainMenuMessage($first_name),
                'reply_markup' => $main_menu_keyboard,
                'parse_mode' => 'HTML'
            ]);
        } else {
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "❌ شما هنوز عضو نیستید!\n\n" .
                         "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                'reply_markup' => $join_keyboard
            ]);
        }
    } elseif ($data == 'help') {
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = createMainMenuKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getMainMenuMessage($first_name),
            'reply_markup' => $main_menu_keyboard,
            'parse_mode' => 'HTML'
        ]);
    } elseif ($data == 'my_channels') {
        $my_channels_keyboard = createMyChannelsKeyboard($user_id);
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "📣 کانال‌های شما:\n\n✅ = ادمین هستید\n❌ = ادمین نیستید",
            'reply_markup' => $my_channels_keyboard
        ]);
    } elseif ($data == 'add_channel') {
        global $database;
        $database->setUserState($user_id, 'waiting_for_channel');

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "➕ افزودن کانال جدید\n\n" .
                     "لطفا یوزرنیم کانال خود را ارسال کنید.\n\n" .
                     "مثال:\n" .
                     "• @mychannel\n" .
                     "• mychannel\n\n" .
                     "یا اگر کانال خصوصی است، ID کانال را ارسال کنید:\n" .
                     "• -1001234567890\n\n" .
                     "❌ برای لغو /cancel را ارسال کنید.",
            'reply_markup' => json_encode(['inline_keyboard' => [
                [
                    [
                        'text' => '❌ لغو',
                        'callback_data' => 'cancel_add_channel'
                    ]
                ]
            ]])
        ]);
    } elseif ($data == 'back_to_main') {
        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = createMainMenuKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getMainMenuMessage($first_name),
            'reply_markup' => $main_menu_keyboard,
            'parse_mode' => 'HTML'
        ]);
    } elseif ($data == 'cancel_add_channel') {
        global $database;
        $database->clearUserState($user_id);

        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = createMainMenuKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getMainMenuMessage($first_name),
            'reply_markup' => $main_menu_keyboard,
            'parse_mode' => 'HTML'
        ]);
    } elseif (strpos($data, 'channel_') === 0) {
        global $database;
        $channel_db_id = substr($data, 8);

        // Get channel details from database
        $stmt = $database->getConnection()->prepare("SELECT * FROM user_channels WHERE id = ? AND user_id = ?");
        $stmt->bind_param("ii", $channel_db_id, $user_id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($channel = $result->fetch_assoc()) {
            $admin_status = $channel['is_admin'] ? '✅ ادمین هستید' : '❌ ادمین نیستید';
            $channel_type = $channel['channel_type'] == 'private' ? 'خصوصی' : 'عمومی';

            $keyboard = [
                [
                    [
                        'text' => '� ویرایش امضا',
                        'callback_data' => 'edit_signature_' . $channel_db_id
                    ]
                ],
                [
                    [
                        'text' => '�🗑 حذف کانال',
                        'callback_data' => 'delete_channel_' . $channel_db_id
                    ]
                ],
                [
                    [
                        'text' => '🔙 بازگشت به لیست کانال‌ها',
                        'callback_data' => 'my_channels'
                    ]
                ]
            ];

            $signature_text = $channel['signature'] ?
                "\n\n🖊 امضا:\n" . $channel['signature'] :
                "\n\n🖊 امضا: تنظیم نشده";

            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "📣 جزئیات کانال:\n\n" .
                         "📌 نام: " . $channel['channel_title'] . "\n" .
                         "🆔 شناسه: " . $channel['channel_id'] . "\n" .
                         "👤 یوزرنیم: " . ($channel['channel_username'] ?: 'ندارد') . "\n" .
                         "🔒 نوع: $channel_type\n" .
                         "👨‍💼 وضعیت: $admin_status\n" .
                         "📅 تاریخ افزودن: " . date('Y/m/d H:i', strtotime($channel['added_at'])) .
                         $signature_text,
                'reply_markup' => json_encode(['inline_keyboard' => $keyboard]),
                'parse_mode' => 'HTML'
            ]);
        }
    } elseif (strpos($data, 'delete_channel_') === 0) {
        global $database;
        $channel_db_id = substr($data, 15);

        // Delete channel from database
        $stmt = $database->getConnection()->prepare("DELETE FROM user_channels WHERE id = ? AND user_id = ?");
        $stmt->bind_param("ii", $channel_db_id, $user_id);

        if ($stmt->execute()) {
            $my_channels_keyboard = createMyChannelsKeyboard($user_id);
            bot('editMessageText', [
                'chat_id' => $chat_id,
                'message_id' => $callback_query->message->message_id,
                'text' => "✅ کانال با موفقیت حذف شد!\n\n📣 کانال‌های شما:\n\n✅ = ادمین هستید\n❌ = ادمین نیستید",
                'reply_markup' => $my_channels_keyboard
            ]);
        }
    } elseif (strpos($data, 'edit_signature_') === 0) {
        global $database;
        $channel_db_id = substr($data, 15);

        // Set state to waiting for new signature
        $database->setUserState($user_id, 'editing_signature', $channel_db_id);

        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => "🖊 ویرایش امضا\n\n" .
                     "لطفا متن جدید امضای خود را ارسال کنید:\n\n" .
                     "<blockquote>🔸 راهنمای قالب بندی متن:\n" .
                     "• *متن* = ضخیم\n" .
                     "• _متن_ = مورب\n" .
                     "• __متن__ = زیرخط دار\n" .
                     "• `متن` = کد\n" .
                     "• [عنوان لینک](آدرس) = لینک\n" .
                     "• >متن = نقل قول</blockquote>\n\n" .
                     "❌ برای لغو /cancel ارسال کنید.",
            'parse_mode' => 'HTML',
            'reply_markup' => json_encode(['inline_keyboard' => [
                [
                    [
                        'text' => '❌ لغو',
                        'callback_data' => 'cancel_edit_signature'
                    ]
                ]
            ]])
        ]);
    } elseif ($data == 'cancel_edit_signature') {
        global $database;
        $database->clearUserState($user_id);

        $first_name = $callback_query->from->first_name ?? 'کاربر';
        $main_menu_keyboard = createMainMenuKeyboard();
        bot('editMessageText', [
            'chat_id' => $chat_id,
            'message_id' => $callback_query->message->message_id,
            'text' => getMainMenuMessage($first_name),
            'reply_markup' => $main_menu_keyboard,
            'parse_mode' => 'HTML'
        ]);

    exit;
}

// Handle regular messages
if (isset($update->message)) {
    $message = $update->message;
    $chat_id = $message->chat->id;
    $user_id = $message->from->id;
    $text = $message->text;
    $first_name = $message->from->first_name ?? 'کاربر';

    // Check if user is in a state
    global $database;
    $user_state = $database->getUserState($user_id);

    if ($text == "/cancel") {
        $database->clearUserState($user_id);
        $main_menu_keyboard = createMainMenuKeyboard();
        sendmessage($chat_id, "❌ عملیات لغو شد.", $main_menu_keyboard);
        return;
    }

    if ($text == "/start" || $text == "/help") {
        $database->clearUserState($user_id);

        if (checkUserMembership($user_id, $required_channels)) {
            $main_menu_keyboard = createMainMenuKeyboard();
            $data = [
                'chat_id' => $chat_id,
                'text' => getMainMenuMessage($first_name),
                'reply_markup' => $main_menu_keyboard,
                'parse_mode' => 'HTML'
            ];
            bot('sendMessage', $data);
        } else {
            $join_keyboard = createJoinKeyboard($required_channels);
            sendmessage($chat_id,
                "سلام $first_name 👋\n\n" .
                "برای استفاده از ربات، لطفا ابتدا در کانال‌های زیر عضو شوید و سپس روی دکمه «بررسی عضویت» کلیک کنید:",
                $join_keyboard
            );
        }
    } elseif ($user_state['state'] == 'waiting_for_channel') {
        // Handle channel input
        $channel_input = trim($text);

        // Clean channel input
        if (strpos($channel_input, '@') === 0) {
            $channel_input = substr($channel_input, 1);
        }

        // Determine if it's a username or ID
        if (is_numeric($channel_input) || (strpos($channel_input, '-') === 0 && is_numeric(substr($channel_input, 1)))) {
            // It's a channel ID (private channel)
            $channel_id = $channel_input;
            $channel_type = 'private';
        } else {
            // It's a username (public channel)
            $channel_id = '@' . $channel_input;
            $channel_type = 'public';
        }

        // Check if user is admin of the channel
        if (checkChannelAdmin($user_id, $channel_id)) {
            // Get channel info
            $channel_info = getChannelInfo($channel_id);

            if ($channel_info) {
                // Store channel info in user state for later use
                $channel_data = json_encode([
                    'channel_id' => $channel_info['id'],
                    'channel_username' => $channel_info['username'],
                    'channel_title' => $channel_info['title'],
                    'channel_type' => $channel_type
                ]);

                // Set state to waiting for signature
                $database->setUserState($user_id, 'waiting_for_signature', $channel_data);

                sendmessage($chat_id,
                    "✅ کانال شناسایی شد!\n\n" .
                    "📣 نام کانال: " . $channel_info['title'] . "\n" .
                    "🆔 شناسه: " . $channel_info['id'] . "\n" .
                    "👤 یوزرنیم: " . ($channel_info['username'] ?: 'ندارد') . "\n" .
                    "🔒 نوع: " . ($channel_type == 'private' ? 'خصوصی' : 'عمومی') . "\n\n" .
                    "🖊 لطفا متن اختصاصی پیشفرض خود را وارد کنید. این متن به انتهای پست‌های کانال‌های شما اضافه خواهد شد مگر اینکه برای کانالی متن اختصاصی دیگری تعریف کنید:\n\n" .
                    "<blockquote>🔸 راهنمای قالب بندی متن:\n" .
                    "• *متن* = ضخیم\n" .
                    "• _متن_ = مورب\n" .
                    "• __متن__ = زیرخط دار\n" .
                    "• `متن` = کد\n" .
                    "• [عنوان لینک](آدرس) = لینک\n" .
                    "• >متن = نقل قول</blockquote>"
                );
            } else {
                sendmessage($chat_id,
                    "❌ خطا در دریافت اطلاعات کانال!\n\n" .
                    "لطفا مطمئن شوید که:\n" .
                    "• ربات به کانال اضافه شده باشد\n" .
                    "• یوزرنیم یا ID کانال صحیح باشد\n\n" .
                    "دوباره تلاش کنید یا /cancel برای لغو:"
                );
            }
        } else {
            sendmessage($chat_id,
                "❌ شما ادمین این کانال نیستید!\n\n" .
                "برای افزودن کانال باید:\n" .
                "• ادمین کانال باشید\n" .
                "• ربات را به کانال اضافه کرده باشید\n\n" .
                "دوباره تلاش کنید یا /cancel برای لغو:"
            );
        }
    } elseif ($user_state['state'] == 'waiting_for_signature') {
        // Handle signature input
        $signature = trim($text);
        $channel_data = json_decode($user_state['data'], true);

        if ($signature && strlen($signature) > 0) {
            // Now add channel to database with signature
            $database->addUserChannel(
                $user_id,
                $channel_data['channel_id'],
                $channel_data['channel_username'],
                $channel_data['channel_title'],
                $channel_data['channel_type'],
                true,
                $signature
            );

            $database->clearUserState($user_id);

            $main_menu_keyboard = createMainMenuKeyboard();
            sendmessage($chat_id,
                "✅ کانال با موفقیت ثبت شد!\n\n" .
                "📣 نام کانال: " . $channel_data['channel_title'] . "\n" .
                "🖊 امضای شما:\n" . $signature . "\n\n" .
                "📝 شما می‌توانید امضا را از بخش مدیریت کانال‌ها تغییر دهید.",
                $main_menu_keyboard
            );
        } else {
            // Signature is required
            sendmessage($chat_id,
                "❌ امضا الزامی است!\n\n" .
                "لطفا متن امضای خود را وارد کنید یا /cancel برای لغو عملیات:"
            );
        }
    } elseif ($user_state['state'] == 'editing_signature') {
        // Handle signature editing
        $signature = trim($text);
        $channel_db_id = $user_state['data'];

        if ($signature && strlen($signature) > 0) {
            // Update channel signature
            $database->updateChannelSignature($channel_db_id, $user_id, $signature);
            $database->clearUserState($user_id);

            $main_menu_keyboard = createMainMenuKeyboard();
            sendmessage($chat_id,
                "✅ امضا با موفقیت به‌روزرسانی شد!\n\n" .
                "🖊 امضای جدید:\n" . $signature,
                $main_menu_keyboard
            );
        } else {
            // Signature is required
            sendmessage($chat_id,
                "❌ امضا نمی‌تواند خالی باشد!\n\n" .
                "لطفا متن امضای خود را وارد کنید یا /cancel برای لغو عملیات:"
            );
        }
    }
}
?>