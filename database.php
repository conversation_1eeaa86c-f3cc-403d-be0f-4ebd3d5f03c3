<?php

require_once 'config.php';

class Database {
    private $connection;
    
    public function __construct() {
        global $DB_HOST, $DB_USER, $DB_PASSWORD, $DB_NAME;
        
        $this->connection = new mysqli($DB_HOST, $DB_USER, $DB_PASSWORD, $DB_NAME);
        
        if ($this->connection->connect_error) {
            die("Connection failed: " . $this->connection->connect_error);
        }
        
        $this->connection->set_charset("utf8mb4");
        $this->createTables();
    }
    
    private function createTables() {
        // Create user_states table for managing user states
        $sql = "CREATE TABLE IF NOT EXISTS user_states (
            user_id BIGINT PRIMARY KEY,
            state VARCHAR(50) DEFAULT '',
            data TEXT DEFAULT ''
        )";
        $this->connection->query($sql);
        
        // Create user_channels table for storing user channels
        $sql = "CREATE TABLE IF NOT EXISTS user_channels (
            id INT AUTO_INCREMENT PRIMARY KEY,
            user_id BIGINT NOT NULL,
            channel_id VARCHAR(255) NOT NULL,
            channel_username VARCHAR(255) DEFAULT '',
            channel_title VARCHAR(255) DEFAULT '',
            channel_type ENUM('public', 'private') DEFAULT 'public',
            is_admin BOOLEAN DEFAULT FALSE,
            signature TEXT DEFAULT '',
            added_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            UNIQUE KEY unique_user_channel (user_id, channel_id)
        )";
        $this->connection->query($sql);
    }
    
    public function setUserState($user_id, $state, $data = '') {
        $stmt = $this->connection->prepare("INSERT INTO user_states (user_id, state, data) VALUES (?, ?, ?) ON DUPLICATE KEY UPDATE state = ?, data = ?");
        $stmt->bind_param("issss", $user_id, $state, $data, $state, $data);
        return $stmt->execute();
    }
    
    public function getUserState($user_id) {
        $stmt = $this->connection->prepare("SELECT state, data FROM user_states WHERE user_id = ?");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($row = $result->fetch_assoc()) {
            return $row;
        }
        return ['state' => '', 'data' => ''];
    }
    
    public function clearUserState($user_id) {
        $stmt = $this->connection->prepare("DELETE FROM user_states WHERE user_id = ?");
        $stmt->bind_param("i", $user_id);
        return $stmt->execute();
    }
    
    public function addUserChannel($user_id, $channel_id, $channel_username = '', $channel_title = '', $channel_type = 'public', $is_admin = false, $signature = '') {
        $stmt = $this->connection->prepare("INSERT INTO user_channels (user_id, channel_id, channel_username, channel_title, channel_type, is_admin, signature) VALUES (?, ?, ?, ?, ?, ?, ?) ON DUPLICATE KEY UPDATE channel_username = ?, channel_title = ?, channel_type = ?, is_admin = ?, signature = ?");
        $stmt->bind_param("issssissssis", $user_id, $channel_id, $channel_username, $channel_title, $channel_type, $is_admin, $signature, $channel_username, $channel_title, $channel_type, $is_admin, $signature);
        return $stmt->execute();
    }
    
    public function getUserChannels($user_id) {
        $stmt = $this->connection->prepare("SELECT * FROM user_channels WHERE user_id = ? ORDER BY added_at DESC");
        $stmt->bind_param("i", $user_id);
        $stmt->execute();
        $result = $stmt->get_result();
        
        $channels = [];
        while ($row = $result->fetch_assoc()) {
            $channels[] = $row;
        }
        return $channels;
    }
    
    public function removeUserChannel($user_id, $channel_id) {
        $stmt = $this->connection->prepare("DELETE FROM user_channels WHERE user_id = ? AND channel_id = ?");
        $stmt->bind_param("is", $user_id, $channel_id);
        return $stmt->execute();
    }

    public function updateChannelSignature($channel_db_id, $user_id, $signature) {
        $stmt = $this->connection->prepare("UPDATE user_channels SET signature = ? WHERE id = ? AND user_id = ?");
        $stmt->bind_param("sii", $signature, $channel_db_id, $user_id);
        return $stmt->execute();
    }
    
    public function getConnection() {
        return $this->connection;
    }
}

// Create global database instance
$database = new Database();

?>
